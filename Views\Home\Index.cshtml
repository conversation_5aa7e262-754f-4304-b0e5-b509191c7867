﻿@{
    ViewData["Title"] = "Home Page";
}

<div class="container">
    <div class="row">
        <div class="col-12">
            <div class="jumbotron bg-primary text-white rounded p-5 mb-5">
                <div class="text-center">
                    <h1 class="display-4 fw-bold">
                        <i class="fas fa-building me-3"></i>Welcome to MvcCustomerApp
                    </h1>
                    <p class="lead">Professional Customer Management System</p>
                    <hr class="my-4 bg-white">
                    <p>Manage your customers efficiently with our comprehensive CRM solution.</p>
                    <a class="btn btn-light btn-lg" asp-controller="Customer" asp-action="Index" role="button">
                        <i class="fas fa-users me-2"></i>Manage Customers
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-4 mb-4">
            <div class="card h-100 shadow-sm">
                <div class="card-body text-center">
                    <i class="fas fa-users fa-3x text-primary mb-3"></i>
                    <h5 class="card-title">Customer Management</h5>
                    <p class="card-text">Add, edit, and manage your customer database with ease.</p>
                    <a href="@Url.Action("Index", "Customer")" class="btn btn-primary">
                        <i class="fas fa-arrow-right me-1"></i>Get Started
                    </a>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-4">
            <div class="card h-100 shadow-sm">
                <div class="card-body text-center">
                    <i class="fas fa-search fa-3x text-success mb-3"></i>
                    <h5 class="card-title">Advanced Search</h5>
                    <p class="card-text">Quickly find customers using our powerful search functionality.</p>
                    <a href="@Url.Action("Index", "Customer")" class="btn btn-success">
                        <i class="fas fa-search me-1"></i>Search Now
                    </a>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-4">
            <div class="card h-100 shadow-sm">
                <div class="card-body text-center">
                    <i class="fas fa-shield-alt fa-3x text-warning mb-3"></i>
                    <h5 class="card-title">Secure Access</h5>
                    <p class="card-text">Your data is protected with ASP.NET Core Identity authentication.</p>
                    <a href="@Url.Action("Privacy", "Home")" class="btn btn-warning">
                        <i class="fas fa-info-circle me-1"></i>Learn More
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

@model MvcCustomerApp.Models.Customer

@{
    ViewData["Title"] = "Customer Details";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-info text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-user me-2"></i>Customer Details
                    </h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="customer-info">
                                <div class="row mb-4">
                                    <div class="col-sm-3">
                                        <strong class="text-muted">
                                            <i class="fas fa-hashtag me-1"></i>Customer ID:
                                        </strong>
                                    </div>
                                    <div class="col-sm-9">
                                        <span class="badge bg-secondary">#@Model.Id</span>
                                    </div>
                                </div>

                                <div class="row mb-4">
                                    <div class="col-sm-3">
                                        <strong class="text-muted">
                                            <i class="fas fa-user me-1"></i>Full Name:
                                        </strong>
                                    </div>
                                    <div class="col-sm-9">
                                        <h5 class="mb-0">@Model.Name</h5>
                                    </div>
                                </div>

                                <div class="row mb-4">
                                    <div class="col-sm-3">
                                        <strong class="text-muted">
                                            <i class="fas fa-envelope me-1"></i>Email:
                                        </strong>
                                    </div>
                                    <div class="col-sm-9">
                                        <a href="mailto:@Model.Email" class="text-decoration-none">
                                            @Model.Email
                                        </a>
                                        <a href="mailto:@Model.Email" class="btn btn-outline-primary btn-sm ms-2">
                                            <i class="fas fa-envelope me-1"></i>Send Email
                                        </a>
                                    </div>
                                </div>

                                <div class="row mb-4">
                                    <div class="col-sm-3">
                                        <strong class="text-muted">
                                            <i class="fas fa-phone me-1"></i>Phone:
                                        </strong>
                                    </div>
                                    <div class="col-sm-9">
                                        <a href="tel:@Model.Phone" class="text-decoration-none">
                                            @Model.Phone
                                        </a>
                                        <a href="tel:@Model.Phone" class="btn btn-outline-success btn-sm ms-2">
                                            <i class="fas fa-phone me-1"></i>Call Now
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <hr class="my-4">

                    <div class="row">
                        <div class="col-12">
                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a asp-action="Index" class="btn btn-outline-secondary me-md-2">
                                    <i class="fas fa-arrow-left me-1"></i>Back to List
                                </a>
                                <a asp-action="Edit" asp-route-id="@Model?.Id" class="btn btn-warning me-md-2">
                                    <i class="fas fa-edit me-1"></i>Edit Customer
                                </a>
                                <a asp-action="Delete" asp-route-id="@Model?.Id" class="btn btn-outline-danger">
                                    <i class="fas fa-trash me-1"></i>Delete Customer
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

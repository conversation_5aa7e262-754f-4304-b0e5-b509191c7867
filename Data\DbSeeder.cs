using MvcCustomerApp.Models;

namespace MvcCustomerApp.Data
{
    public static class DbSeeder
    {
        public static void SeedDatabase(ApplicationDbContext context)
        {
            // Check if customers already exist
            if (context.Customers.Any())
            {
                return; // Database has been seeded
            }

            // Add sample customers
            var customers = new List<Customer>
            {
                new Customer
                {
                    Name = "<PERSON>",
                    Email = "<EMAIL>",
                    Phone = "(*************"
                },
                new Customer
                {
                    Name = "<PERSON>",
                    Email = "<EMAIL>",
                    Phone = "(*************"
                },
                new Customer
                {
                    Name = "<PERSON>",
                    Email = "<EMAIL>",
                    Phone = "(*************"
                },
                new Customer
                {
                    Name = "<PERSON>",
                    Email = "<EMAIL>",
                    Phone = "(*************"
                },
                new Customer
                {
                    Name = "<PERSON>",
                    Email = "<EMAIL>",
                    Phone = "(*************"
                },
                new Customer
                {
                    Name = "<PERSON>",
                    Email = "<EMAIL>",
                    Phone = "(*************"
                },
                new Customer
                {
                    Name = "<PERSON>",
                    Email = "<EMAIL>",
                    Phone = "(*************"
                },
                new Customer
                {
                    Name = "<PERSON>",
                    Email = "<EMAIL>",
                    Phone = "(*************"
                },
                new Customer
                {
                    Name = "Christopher Lee",
                    Email = "<EMAIL>",
                    Phone = "(*************"
                },
                new Customer
                {
                    Name = "Amanda White",
                    Email = "<EMAIL>",
                    Phone = "(*************"
                }
            };

            context.Customers.AddRange(customers);
            context.SaveChanges();
        }
    }
}

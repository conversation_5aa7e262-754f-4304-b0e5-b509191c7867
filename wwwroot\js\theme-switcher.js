// Interactive Theme Switcher
class ThemeSwitcher {
    constructor() {
        this.currentTheme = localStorage.getItem('theme') || 'teal';
        this.themes = {
            teal: { name: 'Teal Ocean', icon: '🌊' },
            ocean: { name: 'Deep Ocean', icon: '🌊' },
            purple: { name: '<PERSON> Purple', icon: '💜' },
            emerald: { name: 'Emerald Forest', icon: '🌲' },
            sunset: { name: 'Sunset Glow', icon: '🌅' },
            dark: { name: 'Dark Mode', icon: '🌙' }
        };
        this.init();
    }

    init() {
        this.createThemeSwitcher();
        this.applyTheme(this.currentTheme);
        this.bindEvents();
    }

    createThemeSwitcher() {
        // Create main theme switcher
        const switcher = document.createElement('div');
        switcher.className = 'theme-switcher';
        switcher.id = 'theme-switcher';
        switcher.innerHTML = `
            <h6><i class="fas fa-palette me-2"></i>Themes</h6>
            <div class="d-flex flex-wrap justify-content-center">
                ${Object.keys(this.themes).map(theme => `
                    <div class="theme-option ${theme === this.currentTheme ? 'active' : ''}"
                         data-theme="${theme}"
                         title="${this.themes[theme].name}">
                    </div>
                `).join('')}
            </div>
            <div class="text-center mt-2">
                <small class="text-muted">
                    <i class="fas fa-magic me-1"></i>
                    <span id="current-theme-name">${this.themes[this.currentTheme].name}</span>
                </small>
            </div>
        `;
        document.body.appendChild(switcher);

        // Create mobile toggle button
        const toggleBtn = document.createElement('button');
        toggleBtn.className = 'theme-toggle-btn';
        toggleBtn.innerHTML = '<i class="fas fa-palette"></i>';
        toggleBtn.title = 'Change Theme';
        document.body.appendChild(toggleBtn);

        // Mobile toggle functionality
        toggleBtn.addEventListener('click', () => {
            const switcher = document.getElementById('theme-switcher');
            switcher.classList.toggle('mobile-open');

            // Close when clicking outside
            if (switcher.classList.contains('mobile-open')) {
                setTimeout(() => {
                    document.addEventListener('click', this.closeMobileSwitcher, true);
                }, 100);
            }
        });
    }

    closeMobileSwitcher = (e) => {
        const switcher = document.getElementById('theme-switcher');
        const toggleBtn = document.querySelector('.theme-toggle-btn');

        if (!switcher.contains(e.target) && !toggleBtn.contains(e.target)) {
            switcher.classList.remove('mobile-open');
            document.removeEventListener('click', this.closeMobileSwitcher, true);
        }
    }

    bindEvents() {
        document.querySelectorAll('.theme-option').forEach(option => {
            option.addEventListener('click', (e) => {
                const theme = e.target.getAttribute('data-theme');
                this.switchTheme(theme);
            });
        });

        // Add keyboard shortcut (Ctrl + T)
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === 't') {
                e.preventDefault();
                this.cycleTheme();
            }
        });
    }

    switchTheme(theme) {
        if (theme === this.currentTheme) return;

        // Add transition effect
        document.body.style.transition = 'all 0.5s ease';
        
        this.applyTheme(theme);
        this.currentTheme = theme;
        localStorage.setItem('theme', theme);

        // Update active state
        document.querySelectorAll('.theme-option').forEach(option => {
            option.classList.remove('active');
        });
        document.querySelector(`[data-theme="${theme}"]`).classList.add('active');

        // Update theme name
        document.getElementById('current-theme-name').textContent = this.themes[theme].name;

        // Show notification
        this.showThemeNotification(theme);

        // Remove transition after animation
        setTimeout(() => {
            document.body.style.transition = '';
        }, 500);
    }

    applyTheme(theme) {
        document.documentElement.setAttribute('data-theme', theme);
        
        // Update meta theme color for mobile browsers
        let metaThemeColor = document.querySelector('meta[name="theme-color"]');
        if (!metaThemeColor) {
            metaThemeColor = document.createElement('meta');
            metaThemeColor.name = 'theme-color';
            document.head.appendChild(metaThemeColor);
        }
        
        const themeColors = {
            teal: '#20b2aa',
            ocean: '#0077be',
            purple: '#7b1fa2',
            emerald: '#00a86b',
            sunset: '#ff6b35',
            dark: '#bb86fc'
        };
        
        metaThemeColor.content = themeColors[theme];
    }

    cycleTheme() {
        const themeKeys = Object.keys(this.themes);
        const currentIndex = themeKeys.indexOf(this.currentTheme);
        const nextIndex = (currentIndex + 1) % themeKeys.length;
        this.switchTheme(themeKeys[nextIndex]);
    }

    showThemeNotification(theme) {
        // Remove existing notification
        const existing = document.querySelector('.theme-notification');
        if (existing) existing.remove();

        const notification = document.createElement('div');
        notification.className = 'theme-notification';
        notification.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 20px 30px;
            border-radius: 10px;
            z-index: 9999;
            font-size: 18px;
            font-weight: 600;
            backdrop-filter: blur(10px);
            animation: themeNotificationFade 2s ease-in-out;
        `;
        
        notification.innerHTML = `
            <i class="fas fa-palette me-2"></i>
            ${this.themes[theme].icon} ${this.themes[theme].name} Theme Applied!
        `;

        document.body.appendChild(notification);

        // Add CSS animation
        if (!document.querySelector('#theme-notification-styles')) {
            const style = document.createElement('style');
            style.id = 'theme-notification-styles';
            style.textContent = `
                @keyframes themeNotificationFade {
                    0% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
                    20% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
                    80% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
                    100% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
                }
            `;
            document.head.appendChild(style);
        }

        setTimeout(() => notification.remove(), 2000);
    }
}

// Initialize theme switcher when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new ThemeSwitcher();
});

// Add some fun easter eggs
document.addEventListener('keydown', (e) => {
    // Konami code for rainbow mode
    if (e.code === 'KeyR' && e.ctrlKey && e.shiftKey) {
        e.preventDefault();
        startRainbowMode();
    }
});

function startRainbowMode() {
    const themes = ['teal', 'ocean', 'purple', 'emerald', 'sunset', 'dark'];
    let index = 0;
    
    const interval = setInterval(() => {
        document.documentElement.setAttribute('data-theme', themes[index]);
        index = (index + 1) % themes.length;
    }, 500);
    
    // Stop after 10 seconds
    setTimeout(() => {
        clearInterval(interval);
        // Restore saved theme
        const savedTheme = localStorage.getItem('theme') || 'teal';
        document.documentElement.setAttribute('data-theme', savedTheme);
    }, 10000);
    
    // Show rainbow notification
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: linear-gradient(45deg, #ff0000, #ff7f00, #ffff00, #00ff00, #0000ff, #4b0082, #9400d3);
        background-size: 400% 400%;
        animation: rainbow 1s ease infinite;
        color: white;
        padding: 20px 30px;
        border-radius: 10px;
        z-index: 9999;
        font-size: 18px;
        font-weight: 600;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
    `;
    notification.innerHTML = '🌈 RAINBOW MODE ACTIVATED! 🌈';
    document.body.appendChild(notification);
    
    if (!document.querySelector('#rainbow-styles')) {
        const style = document.createElement('style');
        style.id = 'rainbow-styles';
        style.textContent = `
            @keyframes rainbow {
                0% { background-position: 0% 50%; }
                50% { background-position: 100% 50%; }
                100% { background-position: 0% 50%; }
            }
        `;
        document.head.appendChild(style);
    }
    
    setTimeout(() => notification.remove(), 3000);
}

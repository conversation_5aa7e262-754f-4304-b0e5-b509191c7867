// Simple Theme Switcher - Fallback version
(function() {
    'use strict';
    
    // Theme configuration
    const themes = {
        teal: 'Teal Ocean',
        ocean: 'Deep Ocean', 
        purple: 'Royal Purple',
        emerald: 'Emerald Forest',
        sunset: 'Sunset Glow',
        dark: 'Dark Mode'
    };
    
    let currentTheme = 'teal';
    
    // Try to get saved theme
    try {
        currentTheme = localStorage.getItem('theme') || 'teal';
    } catch (e) {
        console.warn('localStorage not available, using default theme');
    }
    
    // Apply theme immediately
    function applyTheme(theme) {
        try {
            document.documentElement.setAttribute('data-theme', theme);
            
            // Update meta theme color for mobile
            let metaThemeColor = document.querySelector('meta[name="theme-color"]');
            if (!metaThemeColor) {
                metaThemeColor = document.createElement('meta');
                metaThemeColor.name = 'theme-color';
                document.head.appendChild(metaThemeColor);
            }
            
            const colors = {
                teal: '#20b2aa',
                ocean: '#0077be',
                purple: '#7b1fa2',
                emerald: '#00a86b',
                sunset: '#ff6b35',
                dark: '#bb86fc'
            };
            
            metaThemeColor.content = colors[theme] || colors.teal;
        } catch (error) {
            console.error('Error applying theme:', error);
        }
    }
    
    // Save theme
    function saveTheme(theme) {
        try {
            localStorage.setItem('theme', theme);
        } catch (e) {
            console.warn('Could not save theme to localStorage');
        }
    }
    
    // Create theme switcher UI
    function createThemeSwitcher() {
        try {
            // Remove existing switcher and toggle button
            const existing = document.querySelector('.theme-switcher');
            if (existing) existing.remove();
            const existingBtn = document.querySelector('.theme-toggle-btn');
            if (existingBtn) existingBtn.remove();

            const switcher = document.createElement('div');
            switcher.className = 'theme-switcher';
            switcher.innerHTML = `
                <h6><i class="fas fa-palette me-2"></i>Themes</h6>
                <div class="d-flex flex-wrap justify-content-center">
                    ${Object.keys(themes).map(theme => `
                        <div class="theme-option ${theme === currentTheme ? 'active' : ''}"
                             data-theme="${theme}"
                             title="${themes[theme]}">
                        </div>
                    `).join('')}
                </div>
                <div class="text-center mt-2">
                    <small class="text-muted">
                        <i class="fas fa-magic me-1"></i>
                        <span id="current-theme-name">${themes[currentTheme]}</span>
                    </small>
                </div>
            `;

            document.body.appendChild(switcher);

            // Create toggle button
            const toggleBtn = document.createElement('button');
            toggleBtn.className = 'theme-toggle-btn';
            toggleBtn.innerHTML = '<i class="fas fa-palette"></i>';
            toggleBtn.title = 'Toggle Theme Switcher';
            document.body.appendChild(toggleBtn);

            // Add toggle functionality
            toggleBtn.addEventListener('click', function() {
                const isMobile = window.innerWidth <= 768;

                if (isMobile) {
                    switcher.classList.toggle('mobile-open');
                    // Close when clicking outside on mobile
                    if (switcher.classList.contains('mobile-open')) {
                        setTimeout(() => {
                            document.addEventListener('click', closeMobileSwitcher, true);
                        }, 100);
                    }
                } else {
                    switcher.classList.toggle('open');
                    // Close when clicking outside on desktop
                    if (switcher.classList.contains('open')) {
                        setTimeout(() => {
                            document.addEventListener('click', closeDesktopSwitcher, true);
                        }, 100);
                    }
                }
            });

            // Add click handlers for theme options
            switcher.querySelectorAll('.theme-option').forEach(option => {
                option.addEventListener('click', function() {
                    const theme = this.getAttribute('data-theme');
                    switchTheme(theme);
                });
            });

        } catch (error) {
            console.error('Error creating theme switcher:', error);
        }
    }

    // Close functions for toggle functionality
    function closeMobileSwitcher(e) {
        const switcher = document.querySelector('.theme-switcher');
        const toggleBtn = document.querySelector('.theme-toggle-btn');

        if (switcher && toggleBtn && !switcher.contains(e.target) && !toggleBtn.contains(e.target)) {
            switcher.classList.remove('mobile-open');
            document.removeEventListener('click', closeMobileSwitcher, true);
        }
    }

    function closeDesktopSwitcher(e) {
        const switcher = document.querySelector('.theme-switcher');
        const toggleBtn = document.querySelector('.theme-toggle-btn');

        if (switcher && toggleBtn && !switcher.contains(e.target) && !toggleBtn.contains(e.target)) {
            switcher.classList.remove('open');
            document.removeEventListener('click', closeDesktopSwitcher, true);
        }
    }

    // Switch theme
    function switchTheme(theme) {
        if (!themes[theme] || theme === currentTheme) return;
        
        try {
            currentTheme = theme;
            applyTheme(theme);
            saveTheme(theme);
            
            // Update UI
            document.querySelectorAll('.theme-option').forEach(option => {
                option.classList.remove('active');
            });
            
            const activeOption = document.querySelector(`[data-theme="${theme}"]`);
            if (activeOption) {
                activeOption.classList.add('active');
            }
            
            const themeName = document.getElementById('current-theme-name');
            if (themeName) {
                themeName.textContent = themes[theme];
            }
            
            // Show notification
            showNotification(themes[theme]);
            
        } catch (error) {
            console.error('Error switching theme:', error);
        }
    }
    
    // Show notification
    function showNotification(themeName) {
        try {
            // Remove existing notification
            const existing = document.querySelector('.theme-notification');
            if (existing) existing.remove();
            
            const notification = document.createElement('div');
            notification.className = 'theme-notification';
            notification.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: rgba(0, 0, 0, 0.8);
                color: white;
                padding: 15px 25px;
                border-radius: 8px;
                z-index: 9999;
                font-weight: 600;
                pointer-events: none;
            `;
            
            notification.textContent = `${themeName} Theme Applied!`;
            document.body.appendChild(notification);
            
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 2000);
            
        } catch (error) {
            console.error('Error showing notification:', error);
        }
    }
    
    // Initialize
    function init() {
        // Apply theme immediately
        applyTheme(currentTheme);
        
        // Create UI when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', createThemeSwitcher);
        } else {
            createThemeSwitcher();
        }
        
        // Add keyboard shortcut
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 't') {
                e.preventDefault();
                const themeKeys = Object.keys(themes);
                const currentIndex = themeKeys.indexOf(currentTheme);
                const nextIndex = (currentIndex + 1) % themeKeys.length;
                switchTheme(themeKeys[nextIndex]);
            }
        });
    }
    
    // Start immediately
    init();
    
    // Expose globally for debugging
    window.ThemeSwitcher = {
        switchTheme: switchTheme,
        getCurrentTheme: () => currentTheme,
        getThemes: () => themes
    };
    
})();

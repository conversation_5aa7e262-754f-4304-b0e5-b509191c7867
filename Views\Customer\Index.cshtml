@model IEnumerable<MvcCustomerApp.Models.Customer>

@{
    ViewData["Title"] = "Customer Management";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">
                            <i class="fas fa-users me-2"></i>Customer Management
                        </h4>
                        <a asp-action="Create" class="btn btn-light btn-sm">
                            <i class="fas fa-plus me-1"></i>Add New Customer
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    @if (TempData["SuccessMessage"] != null)
                    {
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>@TempData["SuccessMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    }

                    <!-- Search Form -->
                    <form asp-action="Index" method="get" class="mb-4">
                        <div class="row g-3">
                            <div class="col-md-8">
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-search"></i>
                                    </span>
                                    <input type="text" name="searchString" value="@ViewData["CurrentFilter"]" 
                                           class="form-control" placeholder="Search by name, email, or phone..." />
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="d-grid gap-2 d-md-flex">
                                    <button type="submit" class="btn btn-outline-primary">
                                        <i class="fas fa-search me-1"></i>Search
                                    </button>
                                    <a asp-action="Index" class="btn btn-outline-secondary">
                                        <i class="fas fa-times me-1"></i>Clear
                                    </a>
                                </div>
                            </div>
                        </div>
                    </form>

                    <!-- Customer Table -->
                    <div class="table-responsive">
                        <table class="table table-hover table-striped">
                            <thead class="table-dark">
                                <tr>
                                    <th>Name</th>
                                    <th>Email</th>
                                    <th>Phone</th>
                                    <th class="text-center">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @if (Model.Any())
                                {
                                    @foreach (var customer in Model)
                                    {
                                        <tr>
                                            <td>
                                                <strong>@customer.Name</strong>
                                            </td>
                                            <td>
                                                <i class="fas fa-envelope text-muted me-1"></i>
                                                <a href="mailto:@customer.Email" class="text-decoration-none">@customer.Email</a>
                                            </td>
                                            <td>
                                                <i class="fas fa-phone text-muted me-1"></i>
                                                <a href="tel:@customer.Phone" class="text-decoration-none">@customer.Phone</a>
                                            </td>
                                            <td class="text-center">
                                                <div class="btn-group" role="group">
                                                    <a asp-action="Details" asp-route-id="@customer.Id" 
                                                       class="btn btn-outline-info btn-sm" title="View Details">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a asp-action="Edit" asp-route-id="@customer.Id" 
                                                       class="btn btn-outline-warning btn-sm" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <a asp-action="Delete" asp-route-id="@customer.Id" 
                                                       class="btn btn-outline-danger btn-sm" title="Delete">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                }
                                else
                                {
                                    <tr>
                                        <td colspan="4" class="text-center text-muted py-4">
                                            <i class="fas fa-users fa-3x mb-3 d-block"></i>
                                            <h5>No customers found</h5>
                                            <p>@(ViewData["CurrentFilter"] != null ? "Try adjusting your search criteria." : "Get started by adding your first customer.")</p>
                                            @if (ViewData["CurrentFilter"] == null)
                                            {
                                                <a asp-action="Create" class="btn btn-primary">
                                                    <i class="fas fa-plus me-1"></i>Add First Customer
                                                </a>
                                            }
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    @if ((int)ViewData["TotalPages"] > 1)
                    {
                        <nav aria-label="Customer pagination">
                            <ul class="pagination justify-content-center">
                                @if ((bool)ViewData["HasPreviousPage"])
                                {
                                    <li class="page-item">
                                        <a class="page-link" asp-action="Index" 
                                           asp-route-pageNumber="@((int)ViewData["CurrentPage"] - 1)"
                                           asp-route-searchString="@ViewData["CurrentFilter"]">
                                            <i class="fas fa-chevron-left"></i> Previous
                                        </a>
                                    </li>
                                }

                                @for (int i = Math.Max(1, (int)ViewData["CurrentPage"] - 2); 
                                      i <= Math.Min((int)ViewData["TotalPages"], (int)ViewData["CurrentPage"] + 2); 
                                      i++)
                                {
                                    <li class="page-item @(i == (int)ViewData["CurrentPage"] ? "active" : "")">
                                        <a class="page-link" asp-action="Index" 
                                           asp-route-pageNumber="@i"
                                           asp-route-searchString="@ViewData["CurrentFilter"]">@i</a>
                                    </li>
                                }

                                @if ((bool)ViewData["HasNextPage"])
                                {
                                    <li class="page-item">
                                        <a class="page-link" asp-action="Index" 
                                           asp-route-pageNumber="@((int)ViewData["CurrentPage"] + 1)"
                                           asp-route-searchString="@ViewData["CurrentFilter"]">
                                            Next <i class="fas fa-chevron-right"></i>
                                        </a>
                                    </li>
                                }
                            </ul>
                        </nav>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

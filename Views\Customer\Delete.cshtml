@model MvcCustomerApp.Models.Customer

@{
    ViewData["Title"] = "Delete Customer";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow border-danger">
                <div class="card-header bg-danger text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>Delete Customer
                    </h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Warning!</strong> Are you sure you want to delete this customer? This action cannot be undone.
                    </div>

                    <div class="customer-info bg-light p-4 rounded">
                        <div class="row mb-3">
                            <div class="col-sm-3">
                                <strong class="text-muted">
                                    <i class="fas fa-hashtag me-1"></i>Customer ID:
                                </strong>
                            </div>
                            <div class="col-sm-9">
                                <span class="badge bg-secondary">#@Model.Id</span>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-sm-3">
                                <strong class="text-muted">
                                    <i class="fas fa-user me-1"></i>Full Name:
                                </strong>
                            </div>
                            <div class="col-sm-9">
                                <h5 class="mb-0">@Model.Name</h5>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-sm-3">
                                <strong class="text-muted">
                                    <i class="fas fa-envelope me-1"></i>Email:
                                </strong>
                            </div>
                            <div class="col-sm-9">
                                @Model.Email
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-sm-3">
                                <strong class="text-muted">
                                    <i class="fas fa-phone me-1"></i>Phone:
                                </strong>
                            </div>
                            <div class="col-sm-9">
                                @Model.Phone
                            </div>
                        </div>
                    </div>

                    <form asp-action="Delete" class="mt-4">
                        <input type="hidden" asp-for="Id" />
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a asp-action="Index" class="btn btn-outline-secondary me-md-2">
                                <i class="fas fa-arrow-left me-1"></i>Back to List
                            </a>
                            <a asp-action="Details" asp-route-id="@Model?.Id" class="btn btn-outline-info me-md-2">
                                <i class="fas fa-eye me-1"></i>View Details
                            </a>
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-trash me-1"></i>Delete Customer
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

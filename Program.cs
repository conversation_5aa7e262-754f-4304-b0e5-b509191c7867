// Ensure the Microsoft.EntityFrameworkCore package is installed in your project.  
// You can install it using the following command in the terminal:  
// dotnet add package Microsoft.EntityFrameworkCore  

// Ensure the required NuGet packages are installed in your project.  
// Run the following commands in the terminal:  
// dotnet add package Microsoft.EntityFrameworkCore  
// dotnet add package Microsoft.AspNetCore.Identity.EntityFrameworkCore  

using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.UI.Services;
using Microsoft.EntityFrameworkCore;
using MvcCustomerApp.Models; // Ensure this namespace contains the ApplicationDbContext class

var builder = WebApplication.CreateBuilder(args);

// Configure services  
builder.Services.AddDbContext<ApplicationDbContext>(options =>
 options.UseSqlServer(builder.Configuration.GetConnectionString("DefaultConnection")));

builder.Services.AddDefaultIdentity<IdentityUser>(options =>
{
    options.SignIn.RequireConfirmedAccount = false;
    options.Password.RequireDigit = true;
    options.Password.RequiredLength = 6;
    options.Password.RequireNonAlphanumeric = false;
    options.Password.RequireUppercase = false;
    options.Password.RequireLowercase = false;
})
.AddEntityFrameworkStores<ApplicationDbContext>();

builder.Services.AddControllersWithViews();

var app = builder.Build();

// Configure the HTTP request pipeline  
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Home/Error");
    app.UseHsts();
}

app.UseHttpsRedirection();
app.UseStaticFiles();

app.UseRouting();

app.UseAuthentication();
app.UseAuthorization();

app.MapControllerRoute(
  name: "default",
  pattern: "{controller=Home}/{action=Index}/{id?}");

app.MapRazorPages();

app.Run();

is_global = true
build_property.TargetFramework = net7.0
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = true
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = MvcCustomerApp
build_property.RootNamespace = MvcCustomerApp
build_property.ProjectDir = C:\Users\<USER>\OneDrive\MvcCustomerApp\MvcCustomerApp\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
build_property.RazorLangVersion = 7.0
build_property.SupportLocalizedComponentNames = 
build_property.GenerateRazorMetadataSourceChecksumAttributes = 
build_property.MSBuildProjectDirectory = C:\Users\<USER>\OneDrive\MvcCustomerApp\MvcCustomerApp
build_property._RazorSourceGeneratorDebug = 
build_property.EffectiveAnalysisLevelStyle = 7.0
build_property.EnableCodeStyleSeverity = 

[C:/Users/<USER>/OneDrive/MvcCustomerApp/MvcCustomerApp/Areas/Identity/Pages/Account/Login.cshtml]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcSWRlbnRpdHlcUGFnZXNcQWNjb3VudFxMb2dpbi5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive/MvcCustomerApp/MvcCustomerApp/Areas/Identity/Pages/Account/Logout.cshtml]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcSWRlbnRpdHlcUGFnZXNcQWNjb3VudFxMb2dvdXQuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive/MvcCustomerApp/MvcCustomerApp/Areas/Identity/Pages/Account/Register.cshtml]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcSWRlbnRpdHlcUGFnZXNcQWNjb3VudFxSZWdpc3Rlci5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive/MvcCustomerApp/MvcCustomerApp/Areas/Identity/Pages/Account/_ViewImports.cshtml]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcSWRlbnRpdHlcUGFnZXNcQWNjb3VudFxfVmlld0ltcG9ydHMuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive/MvcCustomerApp/MvcCustomerApp/Areas/Identity/Pages/_ValidationScriptsPartial.cshtml]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcSWRlbnRpdHlcUGFnZXNcX1ZhbGlkYXRpb25TY3JpcHRzUGFydGlhbC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive/MvcCustomerApp/MvcCustomerApp/Areas/Identity/Pages/_ViewImports.cshtml]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcSWRlbnRpdHlcUGFnZXNcX1ZpZXdJbXBvcnRzLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive/MvcCustomerApp/MvcCustomerApp/Areas/Identity/Pages/_ViewStart.cshtml]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcSWRlbnRpdHlcUGFnZXNcX1ZpZXdTdGFydC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive/MvcCustomerApp/MvcCustomerApp/Views/Customer/Create.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcQ3VzdG9tZXJcQ3JlYXRlLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive/MvcCustomerApp/MvcCustomerApp/Views/Customer/Delete.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcQ3VzdG9tZXJcRGVsZXRlLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive/MvcCustomerApp/MvcCustomerApp/Views/Customer/Details.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcQ3VzdG9tZXJcRGV0YWlscy5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive/MvcCustomerApp/MvcCustomerApp/Views/Customer/Edit.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcQ3VzdG9tZXJcRWRpdC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive/MvcCustomerApp/MvcCustomerApp/Views/Customer/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcQ3VzdG9tZXJcSW5kZXguY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive/MvcCustomerApp/MvcCustomerApp/Views/Home/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcSG9tZVxJbmRleC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive/MvcCustomerApp/MvcCustomerApp/Views/Home/Privacy.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcSG9tZVxQcml2YWN5LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive/MvcCustomerApp/MvcCustomerApp/Views/Shared/Error.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcU2hhcmVkXEVycm9yLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive/MvcCustomerApp/MvcCustomerApp/Views/Shared/_LoginPartial.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcU2hhcmVkXF9Mb2dpblBhcnRpYWwuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive/MvcCustomerApp/MvcCustomerApp/Views/Shared/_ValidationScriptsPartial.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcU2hhcmVkXF9WYWxpZGF0aW9uU2NyaXB0c1BhcnRpYWwuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive/MvcCustomerApp/MvcCustomerApp/Views/_ViewImports.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcX1ZpZXdJbXBvcnRzLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive/MvcCustomerApp/MvcCustomerApp/Views/_ViewStart.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcX1ZpZXdTdGFydC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive/MvcCustomerApp/MvcCustomerApp/Views/Shared/_Layout.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcU2hhcmVkXF9MYXlvdXQuY3NodG1s
build_metadata.AdditionalFiles.CssScope = b-lx62d2exv6

html {
  font-size: 14px;
  height: 100%;
}

@media (min-width: 768px) {
  html {
    font-size: 16px;
  }
}

/* Interactive Color Theme System */
:root {
  --primary-color: #20b2aa;
  --primary-dark: #1a9b94;
  --primary-light: #4dd0c7;
  --secondary-color: #17a2b8;
  --accent-color: #00ced1;
  --theme-name: '<PERSON><PERSON>';
}

/* Teal Theme */
[data-theme="teal"] {
  --primary-color: #20b2aa;
  --primary-dark: #1a9b94;
  --primary-light: #4dd0c7;
  --secondary-color: #17a2b8;
  --accent-color: #00ced1;
}

/* Ocean Blue Theme */
[data-theme="ocean"] {
  --primary-color: #0077be;
  --primary-dark: #005a8b;
  --primary-light: #3399d4;
  --secondary-color: #1e88e5;
  --accent-color: #00bcd4;
}

/* Purple Theme */
[data-theme="purple"] {
  --primary-color: #7b1fa2;
  --primary-dark: #4a148c;
  --primary-light: #ab47bc;
  --secondary-color: #8e24aa;
  --accent-color: #e91e63;
}

/* Emerald Theme */
[data-theme="emerald"] {
  --primary-color: #00a86b;
  --primary-dark: #007a4d;
  --primary-light: #26c281;
  --secondary-color: #00c853;
  --accent-color: #4caf50;
}

/* Sunset Theme */
[data-theme="sunset"] {
  --primary-color: #ff6b35;
  --primary-dark: #e55100;
  --primary-light: #ff8a65;
  --secondary-color: #ff7043;
  --accent-color: #ffc107;
}

/* Dark Theme */
[data-theme="dark"] {
  --primary-color: #bb86fc;
  --primary-dark: #6200ea;
  --primary-light: #e1bee7;
  --secondary-color: #03dac6;
  --accent-color: #cf6679;
}

.btn:focus, .btn:active:focus, .btn-link.nav-link:focus, .form-control:focus, .form-check-input:focus {
  box-shadow: 0 0 0 0.1rem white, 0 0 0 0.25rem var(--primary-color);
}

/* Override Bootstrap Primary Colors with Theme Colors */
.bg-primary {
  background-color: var(--primary-color) !important;
  transition: background-color 0.3s ease;
}

.btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  transition: all 0.3s ease;
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
}

.btn-primary:focus, .btn-primary.focus {
  box-shadow: 0 0 0 0.2rem rgba(32, 178, 170, 0.5);
}

.btn-primary:not(:disabled):not(.disabled):active,
.btn-primary:not(:disabled):not(.disabled).active {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
}

.text-primary {
  color: var(--primary-color) !important;
  transition: color 0.3s ease;
}

/* Custom Theme Jumbotron */
.jumbotron-teal {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  color: white;
  transition: background 0.5s ease;
}

/* Card Header Theme */
.card-header-teal {
  background: linear-gradient(90deg, var(--primary-color) 0%, var(--primary-light) 100%);
  color: white;
  border-bottom: 2px solid var(--primary-dark);
  transition: background 0.5s ease;
}

/* Theme Gradient Buttons */
.btn-teal {
  background: linear-gradient(45deg, var(--primary-color) 0%, var(--accent-color) 100%);
  border: none;
  color: white;
  transition: all 0.3s ease;
}

.btn-teal:hover {
  background: linear-gradient(45deg, var(--primary-dark) 0%, var(--secondary-color) 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  color: white;
}

/* Pagination Theme */
.pagination .page-item.active .page-link {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  transition: all 0.3s ease;
}

.pagination .page-link {
  color: var(--primary-color);
  transition: color 0.3s ease;
}

.pagination .page-link:hover {
  color: var(--primary-dark);
  background-color: rgba(0, 0, 0, 0.05);
  border-color: var(--primary-light);
}

/* Table Header Theme */
.table-dark {
  background-color: var(--primary-dark) !important;
  transition: background-color 0.3s ease;
}

/* Alert Success Theme */
.alert-success {
  background-color: rgba(0, 0, 0, 0.05);
  border-color: var(--primary-light);
  color: var(--primary-dark);
  transition: all 0.3s ease;
}

/* Theme Switcher Styles */
.theme-switcher {
  position: fixed;
  top: 80px;
  right: 20px;
  z-index: 1050;
  background: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 15px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transform: translateX(100%);
  transition: transform 0.3s ease;
}

.theme-switcher.open {
  transform: translateX(0);
}

.theme-switcher h6 {
  margin-bottom: 10px;
  font-weight: 600;
  color: #333;
}

.theme-option {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 3px solid transparent;
  cursor: pointer;
  margin: 5px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.theme-option:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.theme-option.active {
  border-color: #333;
  transform: scale(1.15);
}

.theme-option[data-theme="teal"] {
  background: linear-gradient(45deg, #20b2aa, #00ced1);
}

.theme-option[data-theme="ocean"] {
  background: linear-gradient(45deg, #0077be, #00bcd4);
}

.theme-option[data-theme="purple"] {
  background: linear-gradient(45deg, #7b1fa2, #e91e63);
}

.theme-option[data-theme="emerald"] {
  background: linear-gradient(45deg, #00a86b, #4caf50);
}

.theme-option[data-theme="sunset"] {
  background: linear-gradient(45deg, #ff6b35, #ffc107);
}

.theme-option[data-theme="dark"] {
  background: linear-gradient(45deg, #bb86fc, #03dac6);
}

/* Mobile Theme Switcher */
@media (max-width: 768px) {
  .theme-switcher {
    top: 70px;
    right: 10px;
    padding: 10px;
    transform: scale(0.9);
  }

  .theme-option {
    width: 35px;
    height: 35px;
    margin: 3px;
  }
}

/* Theme Switcher Toggle Button */
.theme-toggle-btn {
  position: fixed;
  top: 80px;
  right: -20px;
  width: 50px;
  height: 50px;
  border-radius: 50% 0 0 50%;
  background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
  border: none;
  color: white;
  font-size: 18px;
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.2);
  z-index: 1051;
  transition: all 0.3s ease;
  display: block;
  cursor: pointer;
}

.theme-toggle-btn:hover {
  right: -15px;
  box-shadow: -4px 0 15px rgba(0, 0, 0, 0.3);
}

@media (max-width: 768px) {
  .theme-toggle-btn {
    bottom: 20px;
    right: 20px;
    top: auto;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    font-size: 24px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  }

  .theme-toggle-btn:hover {
    right: 20px;
    transform: scale(1.1);
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.4);
  }

  .theme-switcher {
    display: none;
    position: fixed;
    bottom: 90px;
    right: 20px;
    top: auto;
    transform: translateX(0);
  }

  .theme-switcher.mobile-open {
    display: block;
    animation: slideUp 0.3s ease;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Smooth transitions for themed elements */
.bg-primary, .btn-primary, .text-primary, .jumbotron-teal, .card-header-teal, .btn-teal,
.pagination .page-item.active .page-link, .pagination .page-link, .table-dark, .alert-success {
  transition: all 0.3s ease;
}

/* Sticky Footer Layout */
body {
  margin: 0;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
}

footer {
  margin-top: auto;
}